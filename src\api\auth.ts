import api from "@/lib/axios";
import { LoginResponse, RegisterRequest } from "@/types/auth";

const BASE_ROUTE = "/Auth";
export const loginApi = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  const { data } = await api.post<LoginResponse>(BASE_ROUTE, {
    email,
    password,
  });
  localStorage.setItem("token", data.token);
  localStorage.setItem("refreshToken", data.refreshToken);
  return data;
};

export const registerApi = async (request: RegisterRequest) => {
  // This end point expects multipart/formData
  const formData = new FormData();
  Object.entries(request).forEach(([key, value]) => {
    formData.append(key, value);
  });

  const data = await api.post(`${BASE_ROUTE}/register`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return data;
};

export const externalLoginApi = async (provider: string) => {
  const data = await api.post(`${BASE_ROUTE}/externallogin`, provider);
  return data;
};

export const externalRegisterApi = async (provider: string) => {
  const data = await api.post<LoginResponse>(`${BASE_ROUTE}/signin`, provider);
  return data;
};

export const resetPasswordApi = async ({
  email,
  token,
  newPassword,
}: {
  email: string;
  token: string;
  newPassword: string;
}) => {
  const { data } = await api.post(`${BASE_ROUTE}/reset-password`, {
    email,
    token,
    newPassword,
  });
  return data;
};
