"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Check<PERSON>ircle, XCircle, Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";

interface ConfirmationStatusProps {}

type ConfirmationState = "loading" | "success" | "error" | "invalid";

const ConfirmationStatus: React.FC<ConfirmationStatusProps> = ({}) => {
  const searchParams = useSearchParams();
  const email = searchParams.get("email") || "";
  const token = searchParams.get("token") || "";

  const [status, setStatus] = useState<ConfirmationState>("loading");
  const [countdown, setCountdown] = useState(5);
  const router = useRouter();
  const t = useTranslations();

  useEffect(() => {
    const confirmEmail = async () => {
      try {
        const response = await fetch(
          "https://hala-reservation.runasp.net/api/Auth/confirm-email",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, token }),
          },
        );

        if (response.ok) {
          setStatus("success");
          // Start countdown for redirect
          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                const searchParams = new URLSearchParams({
                  email,
                });

                router.push("/?" + searchParams.toString());
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          console.error("Email confirmation failed");
          setStatus("error");
        }
      } catch (error) {
        console.error("Email confirmation error:", error);
        setStatus("error");
      }
    };

    if (email && token) {
      confirmEmail();
    } else {
      setStatus("invalid");
    }
  }, [email, token, router]);

  const getStatusIcon = () => {
    switch (status) {
      case "loading":
        return <Loader2 className="text-primary h-16 w-16 animate-spin" />;
      case "success":
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case "error":
        return <XCircle className="h-16 w-16 text-red-500" />;
      case "invalid":
        return <Mail className="h-16 w-16 text-gray-400" />;
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case "loading":
        return {
          title: "Confirming Your Email",
          description: "Please wait while we verify your email address...",
        };
      case "success":
        return {
          title: "Email Confirmed Successfully!",
          description: `Welcome to Hala! Your email has been verified. Redirecting to home page in ${countdown} seconds...`,
        };
      case "error":
        return {
          title: "Confirmation Failed",
          description:
            "We couldn't verify your email. The link may be expired or invalid.",
        };
      case "invalid":
        return {
          title: "Invalid Confirmation Link",
          description: "The confirmation link is missing required information.",
        };
      default:
        return { title: "", description: "" };
    }
  };

  const { title, description } = getStatusMessage();

  return (
    <Card
      className="mx-auto w-full max-w-md shadow-lg transition-all duration-500 hover:shadow-xl"
      style={{
        maxWidth: "28rem",
        margin: "0 auto",
        backgroundColor: "#ffffff",
        borderRadius: "0.75rem",
        boxShadow:
          "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        border: "1px solid #e5e7eb",
      }}
    >
      <CardContent
        className="p-8 text-center"
        style={{ padding: "2rem", textAlign: "center" }}
      >
        <div className="mb-6 flex justify-center transition-transform duration-300 hover:scale-110">
          {getStatusIcon()}
        </div>

        <h2 className="text-foreground mb-4 text-2xl font-bold transition-colors duration-300">
          {title}
        </h2>

        <p className="text-muted-foreground mb-6 leading-relaxed">
          {description}
        </p>

        {email && (
          <div className="bg-muted mb-6 rounded-lg p-3">
            <p className="text-muted-foreground text-sm">Email:</p>
            <p className="text-foreground font-medium">{email}</p>
          </div>
        )}

        {status === "error" && (
          <div className="space-y-3">
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="w-full"
            >
              Try Again
            </Button>
            <Button onClick={() => router.push("/")} className="w-full">
              Go to Home
            </Button>
          </div>
        )}

        {status === "invalid" && (
          <Button onClick={() => router.push("/")} className="w-full">
            Go to Home
          </Button>
        )}

        {status === "success" && (
          <div className="space-y-4">
            <div className="h-2 w-full rounded-full bg-gray-200">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-1000 ease-linear"
                style={{ width: `${((5 - countdown) / 5) * 100}%` }}
              ></div>
            </div>
            <Button onClick={() => router.push("/")} className="w-full">
              Continue to Home
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ConfirmationStatus;
