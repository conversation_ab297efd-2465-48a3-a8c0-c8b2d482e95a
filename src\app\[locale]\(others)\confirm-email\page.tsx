import { Check<PERSON>ir<PERSON>, Mail, Shield } from "lucide-react";
import { Suspense } from "react";
import ConfirmationStatus from "./components/ConfirmationStatus";
import LoadingSpinner from "./components/LoadingSpinner";

function ConfirmEmailContent() {
  return (
    <div
      className="bg-background min-h-screen"
      style={{ minHeight: "100vh", backgroundColor: "#ffffff" }}
    >
      <section
        className="from-primary/5 via-background to-primary/10 relative overflow-hidden bg-gradient-to-br py-20"
        style={{
          padding: "5rem 0",
          background:
            "linear-gradient(to bottom right, rgba(39, 159, 199, 0.05), #ffffff, rgba(39, 159, 199, 0.1))",
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Background decorations */}
        <div className="absolute top-10 right-10 opacity-10">
          <Mail className="text-primary h-32 w-32" />
        </div>
        <div className="absolute bottom-10 left-10 opacity-10">
          <Shield className="text-primary h-24 w-24" />
        </div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform opacity-5">
          <CheckCircle className="text-primary h-48 w-48" />
        </div>

        <div className="relative mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
              <Mail className="text-primary mr-2 h-4 w-4" />
              <span className="text-primary text-sm font-semibold">
                Email Verification
              </span>
            </div>

            <h1
              className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl"
              style={{
                fontSize: "2.25rem",
                fontWeight: "bold",
                marginBottom: "1.5rem",
                color: "#1f2937",
                lineHeight: "1.2",
              }}
            >
              Verify Your Email Address
            </h1>
          </div>
          <Suspense fallback={<LoadingSpinner />}>
            <ConfirmationStatus />
          </Suspense>
        </div>
      </section>
    </div>
  );
}

export default function ConfirmEmailPage() {
  return <ConfirmEmailContent />;
}
