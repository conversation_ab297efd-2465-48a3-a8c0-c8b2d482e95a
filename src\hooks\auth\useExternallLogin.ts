import { useMutation, useQueryClient } from "@tanstack/react-query";
import { externalLoginApi } from "@/api/auth";
import { useAuthStore } from "@/store/authState";

export const useExternalLogin = () => {
  const queryClient = useQueryClient();
  const { setAuth } = useAuthStore();
  return useMutation({
    mutationFn: externalLoginApi,
    onSuccess: (data) => {
      console.log("data", data);

      if (data && data.data.token) {
        setAuth(
          {
            firstName: data.data.firstName,
            lastName: data.data.lastName,
            id: data.data.id,
            email: data.data.email,
          },
          data.data.token,
          data.data.expiresIn,
          data.data.refreshToken,
        );
      }
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
};
